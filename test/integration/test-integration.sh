#!/bin/bash
# note this script only runs on the docker env
function usage() {
    echo "usage: $0 [-m local|docker] [-h] [postgres_url]"
    exit $1
}

cleanup() {
    psql "$POSTGRES_ADMIN_URL" -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true
}

check_docker_is_running() {
    if ! docker compose ps app db >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

if [[ ! -z "$ENVIRONMENT" && "$ENVIRONMENT" != "dev" ]]; then
    echo "error: this script should not be run on $ENVIRONMENT environments" >&2
    exit 1
fi


POSTGRES_USER="signalsd-dev"
POSTGRES_PASSWORD=""
TEDB_NAME="signalsd_test"
HOST=localhost
PORT=15432

MODE=""

while getopts "h" opt; do
    case $opt in
        h) usage 0 ;;
        \?) usage 1 ;;
    esac
done

if ! check_docker_is_running; then
    echo "error: docker app is not running. Please start the docker app and try again." >&2
    exit 1
fi


TEST_DATABASE_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${HOST}:${PORT}/${DB_NAME}?sslmode=disable"
POSTGRES_ADMIN_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${HOST}:${PORT}/postgres?sslmode=disable"

echo "🚀 Running signalsd integration tests"
echo
echo "⚙️ Drop and recreate up $DB_NAME"
echo
docker compose exec -it db psql -U $POSTGRES_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"  && \
docker compose exec -it db psql -U $POSTGRES_USER -d postgres -c "CREATE DATABASE $DB_NAME;"
if [ $? -ne 0 ]; then
    echo "❌ Failed to drop and recreate database"
    exit 1
fi

echo
echo "⚙️ running migrations on $DB_NAME database"
echo

docker compose exec app bash -c "cd /signalsd/app && goose -dir sql/schema postgres postgres://${POSTGRES_USER}:@db:5432/${DB_NAME}?sslmode=disable up"
if [ $? -ne 0 ]; then
    echo "❌ Failed to apply migrations"
    exit 1
fi

echo
echo "⚙️️ Running integration tests..."
echo
# Run the integration tests
cd ../../app
go test -v ./test/integration -run "Integration" -timeout 30s

echo
echo "⚙️ Cleaning up test database..."
echo
cleanup

echo "✅ Integration tests completed!"
