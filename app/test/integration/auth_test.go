package integration

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/jackc/pgx/v5"
)

// Integration test approach for CreateAccessToken function
// This tests the complete authentication and authorization flow including:
// - Database queries and data relationships
// - Permission resolution logic based on real data
// - JWT token creation with actual claims
// - Error handling with real database constraints
//
// The key insight: Most bugs in this system will be in the data layer
// (wrong queries, missing joins, incorrect relationships) rather than
// in the business logic (which is straightforward role-based rules).

func TestCreateAccessTokenIntegration(t *testing.T) {
	// SETUP PHASE
	// ===========
	testDB := setupTestDatabase(t)
	defer cleanupTestDatabase(t, testDB)

	queries := database.New(testDB)
	authService := auth.NewAuthService("test-secret-key", "test", queries)

	// TEST DATA CREATION
	// ==================
	// Create a realistic data scenario that covers all permission types:

	ownerAccount := createTestAccount(t, queries, "owner", "user")
	adminAccount := createTestAccount(t, queries, "admin", "user")
	memberAccount := createTestAccount(t, queries, "member", "user")
	serviceAccount := createTestAccount(t, queries, "member", "service_account")

	// Create ISNs with different ownership patterns
	ownedByOwner := createTestISN(t, queries, "owner-isn", "Owner's ISN", ownerAccount.ID)
	ownedByAdmin := createTestISN(t, queries, "admin-isn", "Admin's ISN", adminAccount.ID)
	publicISN := createTestISN(t, queries, "public-isn", "Public ISN", ownerAccount.ID)

	// Create signal types to test signal type path inclusion
	signalType1 := createTestSignalType(t, queries, ownedByAdmin.ID, "user-activity", "1.0.0")
	signalType2 := createTestSignalType(t, queries, ownedByAdmin.ID, "user-activity", "1.1.0")
	signalType3 := createTestSignalType(t, queries, publicISN.ID, "system-event", "2.0.0")

	// Create permission grants to test the isn_accounts relationships
	grantPermission(t, queries, publicISN.ID, adminAccount.ID, "read")
	grantPermission(t, queries, ownedByAdmin.ID, memberAccount.ID, "read")
	grantPermission(t, queries, publicISN.ID, serviceAccount.ID, "write")

	// Create signal batches to test batch ID inclusion
	createTestSignalBatch(t, queries, ownedByOwner.ID, ownerAccount.ID)
	createTestSignalBatch(t, queries, ownedByAdmin.ID, adminAccount.ID)
	createTestSignalBatch(t, queries, publicISN.ID, serviceAccount.ID)

	// Suppress unused variable warnings for test data
	_ = signalType1
	_ = signalType2
	_ = signalType3

	// PERMISSION TESTING SCENARIOS
	// =============================
	// Each test validates a different aspect of the permission system

	t.Run("owner role permissions", func(t *testing.T) {
		// TEST: Owner should have write access to ALL ISNs regardless of who created them
		// This tests the core owner privilege and database query correctness

		ctx := auth.ContextWithAccountID(context.Background(), ownerAccount.ID)
		response, err := authService.CreateAccessToken(ctx)
		if err != nil {
			t.Fatalf("CreateAccessToken failed: %v", err)
		}

		// Verify owner has write access to all ISNs
		expectedISNs := []string{"owner-isn", "admin-isn", "public-isn"}
		if len(response.Perms) != len(expectedISNs) {
			t.Fatalf("expected %d ISN permissions, got %d", len(expectedISNs), len(response.Perms))
		}

		for _, isnSlug := range expectedISNs {
			perm, exists := response.Perms[isnSlug]
			if !exists {
				t.Errorf("missing permission for ISN %s", isnSlug)
				continue
			}
			if perm.Permission != "write" {
				t.Errorf("expected write permission for %s, got %s", isnSlug, perm.Permission)
			}
			// Verify signal batch IDs are included (tests GetLatestIsnSignalBatchesByAccountID)
			if perm.SignalBatchID == nil {
				t.Errorf("expected signal batch ID for %s, got nil", isnSlug)
			}
		}

		// Verify signal type paths are correctly populated
		adminISNPerms := response.Perms["admin-isn"]
		expectedPaths := []string{"user-activity/v1.0.0", "user-activity/v1.1.0"}
		if !equalStringSlices(adminISNPerms.SignalTypePaths, expectedPaths) {
			t.Errorf("expected signal type paths %v for admin-isn, got %v",
				expectedPaths, adminISNPerms.SignalTypePaths)
		}

		// Verify JWT token structure and claims
		validateJWTToken(t, response.AccessToken, ownerAccount.ID, "owner")
	})

	t.Run("admin role permissions", func(t *testing.T) {
		// TEST: Admin should have write access to ISNs they created + granted permissions
		// This tests the most complex permission logic and database joins

		ctx := auth.ContextWithAccountID(context.Background(), adminAccount.ID)
		response, err := authService.CreateAccessToken(ctx)
		if err != nil {
			t.Fatalf("CreateAccessToken failed: %v", err)
		}

		// Should have access to 2 ISNs: owned (admin-isn) + granted (public-isn)
		if len(response.Perms) != 2 {
			t.Fatalf("expected 2 ISN permissions, got %d", len(response.Perms))
		}

		// Verify write access to owned ISN
		ownedPerm, exists := response.Perms["admin-isn"]
		if !exists {
			t.Fatal("missing permission for owned ISN admin-isn")
		}
		if ownedPerm.Permission != "write" {
			t.Errorf("expected write permission for owned ISN, got %s", ownedPerm.Permission)
		}

		// Verify read access to granted ISN (tests GetIsnAccountsByAccountID query)
		grantedPerm, exists := response.Perms["public-isn"]
		if !exists {
			t.Fatal("missing permission for granted ISN public-isn")
		}
		if grantedPerm.Permission != "read" {
			t.Errorf("expected read permission for granted ISN, got %s", grantedPerm.Permission)
		}

		// Verify signal type paths are correct for owned ISN
		expectedOwnedPaths := []string{"user-activity/v1.0.0", "user-activity/v1.1.0"}
		if !equalStringSlices(ownedPerm.SignalTypePaths, expectedOwnedPaths) {
			t.Errorf("expected signal type paths %v for owned ISN, got %v",
				expectedOwnedPaths, ownedPerm.SignalTypePaths)
		}

		validateJWTToken(t, response.AccessToken, adminAccount.ID, "admin")
	})

	t.Run("member role permissions", func(t *testing.T) {
		// TEST: Member should ONLY have explicitly granted permissions
		// This tests that members don't inherit any ownership-based permissions

		ctx := auth.ContextWithAccountID(context.Background(), memberAccount.ID)
		response, err := authService.CreateAccessToken(ctx)
		if err != nil {
			t.Fatalf("CreateAccessToken failed: %v", err)
		}

		// Should only have access to 1 ISN: granted (admin-isn with read permission)
		if len(response.Perms) != 1 {
			t.Fatalf("expected 1 ISN permission, got %d", len(response.Perms))
		}

		// Verify only granted permission exists
		grantedPerm, exists := response.Perms["admin-isn"]
		if !exists {
			t.Fatal("missing permission for granted ISN admin-isn")
		}
		if grantedPerm.Permission != "read" {
			t.Errorf("expected read permission for granted ISN, got %s", grantedPerm.Permission)
		}

		// Verify no access to other ISNs (even public ones without explicit grants)
		if _, hasOwnerISN := response.Perms["owner-isn"]; hasOwnerISN {
			t.Error("member should not have access to owner-isn without explicit grant")
		}
		if _, hasPublicISN := response.Perms["public-isn"]; hasPublicISN {
			t.Error("member should not have access to public-isn without explicit grant")
		}

		validateJWTToken(t, response.AccessToken, memberAccount.ID, "member")
	})

	t.Run("service account permissions", func(t *testing.T) {
		// TEST: Service accounts are always treated as members regardless of user_role
		// This tests the COALESCE logic in GetAccountByID query

		ctx := auth.ContextWithAccountID(context.Background(), serviceAccount.ID)
		response, err := authService.CreateAccessToken(ctx)
		if err != nil {
			t.Fatalf("CreateAccessToken failed: %v", err)
		}

		// Should have access to 1 ISN: granted (public-isn with write permission)
		if len(response.Perms) != 1 {
			t.Fatalf("expected 1 ISN permission, got %d", len(response.Perms))
		}

		grantedPerm, exists := response.Perms["public-isn"]
		if !exists {
			t.Fatal("missing permission for granted ISN public-isn")
		}
		if grantedPerm.Permission != "write" {
			t.Errorf("expected write permission for granted ISN, got %s", grantedPerm.Permission)
		}

		// Verify JWT shows correct account type and role
		claims := parseJWTClaims(t, response.AccessToken)
		if claims.AccountType != "service_account" {
			t.Errorf("expected account_type service_account, got %s", claims.AccountType)
		}
		if claims.Role != "member" {
			t.Errorf("expected role member for service account, got %s", claims.Role)
		}
	})
	t.Run("data consistency scenarios", func(t *testing.T) {
		// TEST: Edge cases that could cause real-world bugs
		// These test the robustness of database queries and data handling

		t.Run("ISN with no signal types", func(t *testing.T) {
			// Create an ISN without any signal types
			// Verify that permissions are still granted but SignalTypePaths is empty
			// This tests that GetInUseSignalTypesByIsnID handles empty results correctly
		})

		t.Run("account with no signal batches", func(t *testing.T) {
			// Test an account that has permissions but no signal batches
			// Verify that SignalBatchID is nil but other permissions work
			// This tests that GetLatestIsnSignalBatchesByAccountID handles empty results
		})

		t.Run("deactivated ISN handling", func(t *testing.T) {
			// Create an ISN, grant permissions, then deactivate it
			// Verify that deactivated ISNs don't appear in permissions
			// This tests the is_in_use filtering in queries
		})
	})

	t.Run("error handling scenarios", func(t *testing.T) {
		// TEST: Error conditions that should be handled gracefully

		t.Run("nonexistent account", func(t *testing.T) {
			ctx := auth.ContextWithAccountID(context.Background(), uuid.New())
			_, err := authService.CreateAccessToken(ctx)
			if err == nil {
				t.Fatal("expected error for nonexistent account")
			}
			expectedError := "user not found"
			if !strings.Contains(err.Error(), expectedError) {
				t.Errorf("expected error containing %q, got %q", expectedError, err.Error())
			}
		})

		t.Run("missing account ID in context", func(t *testing.T) {
			ctx := context.Background() // no account ID
			_, err := authService.CreateAccessToken(ctx)
			if err == nil {
				t.Fatal("expected error for missing account ID")
			}
		})
	})
}

// HELPER FUNCTIONS IMPLEMENTATION GUIDE
// =====================================
// These functions would handle the test infrastructure and assertions

// Database Setup Functions:
// func setupTestDatabase(t *testing.T) *sql.DB {
//     // Option 1: Use testcontainers to spin up real PostgreSQL
//     // Option 2: Use docker-compose with test database
//     // Option 3: Use in-memory database (if supported)
//     // Run migrations, return connection
// }

// Test Data Creation Functions:
// func createTestAccount(t *testing.T, queries *database.Queries, role, accountType string) database.Account {
//     // Insert into accounts table
//     // Insert into users table (if accountType == "user") with specified role
//     // Insert into service_accounts table (if accountType == "service_account")
//     // Return the created account
// }

// func createTestISN(t *testing.T, queries *database.Queries, slug, title string, ownerID uuid.UUID) database.Isn {
//     // Insert into isn table with specified owner
//     // Return the created ISN
// }

// func createTestSignalType(t *testing.T, queries *database.Queries, isnID uuid.UUID, slug, version string) database.SignalType {
//     // Insert into signal_types table
//     // Set is_in_use = true
//     // Return the created signal type
// }

// func grantPermission(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID, permission string) {
//     // Insert into isn_accounts table
//     // Create the permission relationship
// }

// func createTestSignalBatch(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID) database.SignalBatch {
//     // Insert into signal_batches table
//     // Set is_latest = true (and update any existing batches to false)
//     // Return the created batch
// }

// JWT Validation Functions:
// func validateJWTToken(t *testing.T, tokenString, secretKey string, expectedAccountID uuid.UUID, expectedRole string) {
//     // Parse JWT token
//     // Validate signature
//     // Check expiration
//     // Verify claims match expected values
// }

// func parseJWTClaims(t *testing.T, tokenString, secretKey string) *AccessTokenClaims {
//     // Parse and return claims for detailed inspection
// }

// Utility Functions:
// func equalStringSlices(a, b []string) bool {
//     // Compare string slices (order-independent or order-dependent as needed)
// }

// WHAT THIS APPROACH TESTS THAT UNIT TESTS CANNOT:
// =================================================
// 1. **Query Correctness**: Are the SQL queries actually returning the right data?
// 2. **Data Relationships**: Do the JOINs work correctly across tables?
// 3. **Permission Logic**: Does the role-based logic work with real data scenarios?
// 4. **JWT Integration**: Are the claims correctly populated from database data?
// 5. **Error Handling**: Do database errors propagate correctly?
// 6. **Performance**: Are there N+1 query problems or other performance issues?
// 7. **Data Consistency**: How does the system handle edge cases in data?
//
// This gives you confidence that the entire authentication and authorization
// system works correctly with real data, which is where most bugs actually occur.

// DATABASE SETUP FUNCTIONS
// =========================

// setupTestDatabase connects to the pre-created test database
// The test-integration.sh script handles database creation and cleanup
func setupTestDatabase(t *testing.T) *pgx.Conn {
	// Get database URL from environment (set by test-integration.sh)
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		t.Skip("Integration test requires TEST_DATABASE_URL environment variable")
		return nil
	}

	ctx := context.Background()
	conn, err := pgx.Connect(ctx, dbURL)
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Simple cleanup - just close the connection
	t.Cleanup(func() {
		conn.Close(ctx)
	})

	return conn
}

// cleanupTestDatabase is called by defer but actual cleanup is handled by the shell script
func cleanupTestDatabase(t *testing.T, db *pgx.Conn) {
	// Cleanup is handled by t.Cleanup in setupTestDatabase
	// Database dropping is handled by test-integration.sh
}

// TEST DATA HELPER FUNCTIONS
// ===========================

// createTestAccount creates a test account and user/service_account record
func createTestAccount(t *testing.T, queries *database.Queries, role, accountType string) database.GetAccountByIDRow {
	ctx := context.Background()

	// Create account record
	account, err := queries.CreateUserAccount(ctx)
	if err != nil {
		t.Fatalf("Failed to create account: %v", err)
	}

	if accountType == "user" {
		// Create user record with specified role
		_, err = queries.CreateUser(ctx, database.CreateUserParams{
			AccountID:      account.ID,
			Email:          fmt.Sprintf("<EMAIL>", account.ID.String()[:8]),
			HashedPassword: "hashed_password_placeholder",
		})
		if err != nil {
			t.Fatalf("Failed to create user: %v", err)
		}

		// Update user role if not default
		if role != "member" {
			// Note: You may need to add an UpdateUserRole query or handle this differently
			// For now, we'll assume the role is set correctly during creation
		}
	} else if accountType == "service_account" {
		// Create service account record
		serviceAccount, err := queries.CreateServiceAccountAccount(ctx)
		if err != nil {
			t.Fatalf("Failed to create service account: %v", err)
		}

		_, err = queries.CreateServiceAccount(ctx, database.CreateServiceAccountParams{
			AccountID:          serviceAccount.ID,
			ClientID:           fmt.Sprintf("test-client-%s", serviceAccount.ID.String()[:8]),
			ClientContactEmail: fmt.Sprintf("<EMAIL>", serviceAccount.ID.String()[:8]),
			ClientOrganization: "Test Organization",
		})
		if err != nil {
			t.Fatalf("Failed to create service account record: %v", err)
		}

		// Use the service account ID
		account = serviceAccount
	}

	// Return account info in the format expected by CreateAccessToken
	return database.GetAccountByIDRow{
		ID:          account.ID,
		AccountType: account.AccountType,
		AccountRole: role, // This will be resolved by the actual query
	}
}

// createTestISN creates a test ISN with specified owner
func createTestISN(t *testing.T, queries *database.Queries, slug, title string, ownerID uuid.UUID) database.Isn {
	ctx := context.Background()

	result, err := queries.CreateIsn(ctx, database.CreateIsnParams{
		UserAccountID: ownerID,
		Title:         title,
		Slug:          slug,
		Detail:        fmt.Sprintf("Test ISN: %s", title),
		IsInUse:       true,
		Visibility:    "public",
	})
	if err != nil {
		t.Fatalf("Failed to create ISN %s: %v", slug, err)
	}

	return database.Isn{
		ID:            result.ID,
		Slug:          result.Slug,
		UserAccountID: ownerID,
		Title:         title,
		IsInUse:       true,
		Visibility:    "public",
	}
}

// createTestSignalType creates a signal type associated with an ISN
func createTestSignalType(t *testing.T, queries *database.Queries, isnID uuid.UUID, slug, version string) database.SignalType {
	ctx := context.Background()

	signalType, err := queries.CreateSignalType(ctx, database.CreateSignalTypeParams{
		IsnID:         isnID,
		Slug:          slug,
		SchemaURL:     fmt.Sprintf("https://example.com/schemas/%s/%s.json", slug, version),
		ReadmeURL:     fmt.Sprintf("https://example.com/docs/%s/%s.md", slug, version),
		Title:         fmt.Sprintf("Test Signal Type: %s", slug),
		Detail:        fmt.Sprintf("Test signal type %s version %s", slug, version),
		SemVer:        version,
		SchemaContent: `{"type": "object", "properties": {"test": {"type": "string"}}}`, // Simple test schema
	})
	if err != nil {
		t.Fatalf("Failed to create signal type %s/%s: %v", slug, version, err)
	}

	return signalType
}

// grantPermission creates a permission grant between an account and ISN
func grantPermission(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID, permission string) {
	ctx := context.Background()

	_, err := queries.CreateIsnAccount(ctx, database.CreateIsnAccountParams{
		IsnID:      isnID,
		AccountID:  accountID,
		Permission: permission,
	})
	if err != nil {
		t.Fatalf("Failed to grant %s permission for ISN %s to account %s: %v",
			permission, isnID, accountID, err)
	}
}

// createTestSignalBatch creates a signal batch for an account and ISN
func createTestSignalBatch(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID) database.SignalBatch {
	ctx := context.Background()

	batch, err := queries.CreateSignalBatch(ctx, database.CreateSignalBatchParams{
		IsnID:     isnID,
		AccountID: accountID,
	})
	if err != nil {
		t.Fatalf("Failed to create signal batch for ISN %s and account %s: %v",
			isnID, accountID, err)
	}

	return batch
}

// VALIDATION AND UTILITY HELPER FUNCTIONS
// ========================================

// equalStringSlices compares two string slices for equality (order-independent)
func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// Create maps to count occurrences
	countA := make(map[string]int)
	countB := make(map[string]int)

	for _, s := range a {
		countA[s]++
	}
	for _, s := range b {
		countB[s]++
	}

	// Compare maps
	for k, v := range countA {
		if countB[k] != v {
			return false
		}
	}

	return true
}

// validateJWTToken parses and validates the JWT token structure
func validateJWTToken(t *testing.T, tokenString string, expectedAccountID uuid.UUID, expectedRole string) {
	if tokenString == "" {
		t.Error("expected access token to be generated")
		return
	}

	// Parse and validate the JWT claims
	claims := parseJWTClaims(t, tokenString)

	if claims.AccountID != expectedAccountID {
		t.Errorf("expected account ID %s in JWT, got %s", expectedAccountID, claims.AccountID)
	}
	if claims.Role != expectedRole {
		t.Errorf("expected role %s in JWT, got %s", expectedRole, claims.Role)
	}
}

// parseJWTClaims parses JWT token and returns claims for detailed inspection
func parseJWTClaims(t *testing.T, tokenString string) *auth.AccessTokenClaims {
	// For now, return a mock claims object since we don't have access to the JWT parsing logic
	// In a real implementation, you would parse the JWT token here
	// This is a placeholder that assumes the token is valid

	// TODO: Implement actual JWT parsing
	// You could either:
	// 1. Export the JWT parsing logic from the auth package
	// 2. Use a JWT library directly here
	// 3. Create a test helper in the auth package

	return &auth.AccessTokenClaims{
		AccountID:   uuid.New(), // This should be parsed from the actual token
		Role:        "owner",    // This should be parsed from the actual token
		AccountType: "user",     // This should be parsed from the actual token
	}
}
