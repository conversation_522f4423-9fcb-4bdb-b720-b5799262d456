package integration_test

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/information-sharing-networks/signalsd/app/internal/server/utils"
	"github.com/jackc/pgx/v5"
)

// TestAccessTokenIntegration tests the authentication and authorization flow including:
// - Database queries
// - Permission logic
// - JWT token creation
func TestAccessTokenIntegration(t *testing.T) {
	testDB := setupDbConn(t)

	queries := database.New(testDB)
	authService := auth.NewAuthService("test-secret-key", "test", queries)

	// Create test accounts
	ownerAccount := createTestAccount(t, queries, "owner", "user", "<EMAIL>")
	adminAccount := createTestAccount(t, queries, "admin", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, queries, "member", "user", "<EMAIL>")
	serviceAccount := createTestAccount(t, queries, "member", "service_account", "<EMAIL>")

	// Create ISNs
	ownerISN := createTestISN(t, queries, "owner-isn", "Owner ISN", ownerAccount.ID)
	adminISN := createTestISN(t, queries, "admin-isn", "Admin ISN", adminAccount.ID)
	publicISN := createTestISN(t, queries, "public-isn", "Public ISN", adminAccount.ID)

	// Create signal types
	_ = createTestSignalType(t, queries, ownerISN.ID, "owner ISN signal", "1.0.0")
	_ = createTestSignalType(t, queries, adminISN.ID, "admin ISN signal", "1.0.0")
	_ = createTestSignalType(t, queries, publicISN.ID, "public ISN signal", "1.0.0")

	// Create permission grants to test the isn_accounts relationships
	// note owners automatically get write access to all isns
	// ... and admins get write access to their own isns, so no need to grant explicit perms in this case.
	grantPermission(t, queries, adminISN.ID, memberAccount.ID, "read")
	grantPermission(t, queries, publicISN.ID, serviceAccount.ID, "write")

	// Create signal batches
	createTestSignalBatch(t, queries, ownerISN.ID, ownerAccount.ID)
	createTestSignalBatch(t, queries, adminISN.ID, ownerAccount.ID)
	createTestSignalBatch(t, queries, publicISN.ID, ownerAccount.ID)
	createTestSignalBatch(t, queries, adminISN.ID, adminAccount.ID)
	createTestSignalBatch(t, queries, publicISN.ID, adminAccount.ID)
	createTestSignalBatch(t, queries, publicISN.ID, serviceAccount.ID)

	var validSignalTypePaths = make(map[string]string)

	validSignalTypePaths["owner-isn"] = "owner-isn-signal/v1.0.0"
	validSignalTypePaths["admin-isn"] = "admin-isn-signal/v1.0.0"
	validSignalTypePaths["public-isn"] = "public-isn-signal/v1.0.0"

	t.Run("owner role permissions", func(t *testing.T) {
		// owner should have write access to all ISNs
		expectedPerms := map[string]string{
			"owner-isn":  "write",
			"admin-isn":  "write",
			"public-isn": "write",
		}
		testRolePermissions(t, authService, ownerAccount.ID, "owner", expectedPerms, validSignalTypePaths)
	})

	t.Run("admin role permissions", func(t *testing.T) {
		// admin should have write access to their own ISN plus the granted write permission on public-isn
		expectedPerms := map[string]string{
			"admin-isn":  "write",
			"public-isn": "write",
		}
		testRolePermissions(t, authService, adminAccount.ID, "admin", expectedPerms, validSignalTypePaths)
	})

	t.Run("member role permissions", func(t *testing.T) {
		// member should have been granted read access to admin-isn
		expectedPerms := map[string]string{
			"admin-isn": "read",
		}
		testRolePermissions(t, authService, memberAccount.ID, "member", expectedPerms, validSignalTypePaths)
	})

	t.Run("service account permissions", func(t *testing.T) {
		expectedPerms := map[string]string{
			"public-isn": "write",
		}
		testRolePermissions(t, authService, serviceAccount.ID, "member", expectedPerms, validSignalTypePaths)
	})

	t.Run("data consistency scenarios", func(t *testing.T) {

		t.Run("ISN with no signal types", func(t *testing.T) {
			// Create an ISN without any signal types
			// Verify that permissions are still granted but SignalTypePaths is empty
			// This tests that GetInUseSignalTypesByIsnID handles empty results correctly
		})

		t.Run("account with no signal batches", func(t *testing.T) {
			// Test an account that has permissions but no signal batches
			// Verify that SignalBatchID is nil but other permissions work
			// This tests that GetLatestIsnSignalBatchesByAccountID handles empty results
		})

		t.Run("deactivated ISN handling", func(t *testing.T) {
			// Create an ISN, grant permissions, then deactivate it
			// Verify that deactivated ISNs don't appear in permissions
			// This tests the is_in_use filtering in queries
		})
	})

	t.Run("error handling ", func(t *testing.T) {

		t.Run("nonexistent account", func(t *testing.T) {
			ctx := auth.ContextWithAccountID(context.Background(), uuid.New())
			_, err := authService.CreateAccessToken(ctx)
			if err == nil {
				t.Fatal("expected error for nonexistent account")
			}
			expectedError := "user not found"
			if !strings.Contains(err.Error(), expectedError) {
				t.Errorf("expected error containing %q, got %q", expectedError, err.Error())
			}
		})

		t.Run("missing account ID in context", func(t *testing.T) {
			ctx := context.Background() // no account ID
			_, err := authService.CreateAccessToken(ctx)
			if err == nil {
				t.Fatal("expected error for missing account ID")
			}
		})
	})
}

// setupDbConn connects to the pre-created test database
// The test-integration.sh script handles database creation and cleanup
func setupDbConn(t *testing.T) *pgx.Conn {
	// Get database URL from environment (set by test-integration.sh)
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		t.Skip("Integration test requires TEST_DATABASE_URL environment variable")
		return nil
	}

	ctx := context.Background()
	conn, err := pgx.Connect(ctx, dbURL)
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	t.Cleanup(func() {
		conn.Close(ctx)
	})

	return conn
}

// TEST DATA HELPER FUNCTIONS

// createTestAccount creates entries in account and user/service_account tables
func createTestAccount(t *testing.T, queries *database.Queries, role, accountType string, email string) database.GetAccountByIDRow {
	ctx := context.Background()

	// Create account record
	account, err := queries.CreateUserAccount(ctx)
	if err != nil {
		t.Fatalf("Failed to create account: %v", err)
	}

	switch accountType {
	case "user":
		if role == "owner" { // first user is always the owner
			_, err = queries.CreateOwnerUser(ctx, database.CreateOwnerUserParams{
				AccountID:      account.ID,
				Email:          email,
				HashedPassword: "hashed_password_placeholder",
			})
		} else {
			_, err = queries.CreateUser(ctx, database.CreateUserParams{
				AccountID:      account.ID,
				Email:          email,
				HashedPassword: "hashed_password_placeholder",
			})
		}
		if err != nil {
			t.Fatalf("Failed to create user: %v", err)
		}

		if role == "admin" {
			_, err = queries.UpdateUserAccountToAdmin(ctx, account.ID)
			if err != nil {
				t.Fatalf("Failed to update user to admin: %v", err)
			}
		}

	case "service_account":
		serviceAccount, err := queries.CreateServiceAccountAccount(ctx)
		if err != nil {
			t.Fatalf("Failed to create service account: %v", err)
		}

		_, err = queries.CreateServiceAccount(ctx, database.CreateServiceAccountParams{
			AccountID:          serviceAccount.ID,
			ClientID:           fmt.Sprintf("test-client-%s", serviceAccount.ID.String()[:8]),
			ClientContactEmail: email,
			ClientOrganization: "Test Organization",
		})
		if err != nil {
			t.Fatalf("Failed to create service account record: %v", err)
		}

		// Use the service account ID
		account = serviceAccount
	}

	// Return account info in the format expected by CreateAccessToken
	return database.GetAccountByIDRow{
		ID:          account.ID,
		AccountType: account.AccountType,
		AccountRole: role, // This will be resolved by the actual query
	}
}

// createTestISN creates a test ISN with specified owner
func createTestISN(t *testing.T, queries *database.Queries, slug, title string, ownerID uuid.UUID) database.Isn {
	ctx := context.Background()

	result, err := queries.CreateIsn(ctx, database.CreateIsnParams{
		UserAccountID: ownerID,
		Title:         title,
		Slug:          slug,
		Detail:        fmt.Sprintf("Test ISN: %s", title),
		IsInUse:       true,
		Visibility:    "public",
	})
	if err != nil {
		t.Fatalf("Failed to create ISN %s: %v", slug, err)
	}

	return database.Isn{
		ID:            result.ID,
		Slug:          result.Slug,
		UserAccountID: ownerID,
		Title:         title,
		IsInUse:       true,
		Visibility:    "public",
	}
}

// createTestSignalType creates a signal type associated with an ISN
func createTestSignalType(t *testing.T, queries *database.Queries, isnID uuid.UUID, title string, version string) database.SignalType {
	ctx := context.Background()

	slug, _ := utils.GenerateSlug(title)

	signalType, err := queries.CreateSignalType(ctx, database.CreateSignalTypeParams{
		IsnID:         isnID,
		Slug:          slug,
		SchemaURL:     "https://github.com/information-sharing-networks/signalsd_test_schemas/blob/main/2025.05.13/integration-test-schema.json",
		ReadmeURL:     "https://github.com/information-sharing-networks/signalsd_test_schemas/blob/main/2025.05.13/README.md",
		Title:         title,
		Detail:        "test signal type",
		SemVer:        version,
		SchemaContent: `{"type": "object", "properties": {"test": {"type": "string"}}, "required": ["test"], "additionalProperties": false }`, // Simple test schema`
	})
	if err != nil {
		t.Fatalf("Failed to create signal type %s/%s: %v", slug, version, err)
	}

	return signalType
}

// grantPermission creates a permission grant between an account and ISN
func grantPermission(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID, permission string) {
	ctx := context.Background()

	_, err := queries.CreateIsnAccount(ctx, database.CreateIsnAccountParams{
		IsnID:      isnID,
		AccountID:  accountID,
		Permission: permission,
	})
	if err != nil {
		t.Fatalf("Failed to grant %s permission for ISN %s to account %s: %v",
			permission, isnID, accountID, err)
	}
}

// createTestSignalBatch creates a signal batch for an account and ISN
func createTestSignalBatch(t *testing.T, queries *database.Queries, isnID, accountID uuid.UUID) database.SignalBatch {
	ctx := context.Background()

	batch, err := queries.CreateSignalBatch(ctx, database.CreateSignalBatchParams{
		IsnID:     isnID,
		AccountID: accountID,
	})
	if err != nil {
		t.Fatalf("Failed to create signal batch for ISN %s and account %s: %v",
			isnID, accountID, err)
	}

	return batch
}

// TEST HELPER FUNCTIONS

// testRolePermissions is a helper that tests the permissions for a given account
// tests are done on the returned struct from CreateAccessToken and the parsed JWT token
func testRolePermissions(t *testing.T, authService *auth.AuthService, accountID uuid.UUID, expectedRole string, expectedPerms map[string]string, validSignalTypePaths map[string]string) {
	t.Helper()

	ctx := auth.ContextWithAccountID(context.Background(), accountID)
	response, err := authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("CreateAccessToken failed: %v", err)
	}

	if response.AccountID != accountID {
		t.Errorf("expected account ID %s, got %s", accountID, response.AccountID)
	}

	if response.Role != expectedRole {
		t.Errorf("expected role %s, got %s", expectedRole, response.Role)
	}

	// checks that the response contains expected ISN permissions

	if len(response.Perms) != len(expectedPerms) {
		t.Fatalf("expected %d ISN permissions, got %d", len(expectedPerms), len(response.Perms))
	}

	for isnSlug, expectedPermission := range expectedPerms {
		perm, exists := response.Perms[isnSlug]
		if !exists {
			t.Errorf("missing permission for ISN %s", isnSlug)
			continue
		}
		if perm.Permission != expectedPermission {
			t.Errorf("expected %s permission for %s, got %s", expectedPermission, isnSlug, perm.Permission)
		}

		if perm.Permission == "write" {
			if perm.SignalBatchID == nil {
				t.Errorf("expected signal batch ID for %s, got nil", isnSlug)
			}
		}

		if perm.Permission == "read" {
			if perm.SignalBatchID != nil {
				t.Errorf("expected signal batch to be nil for %s, got %s", isnSlug, perm.SignalBatchID)
			}
		}

		// Verify signal type paths are correctly populated
		if len(perm.SignalTypePaths) != 1 {
			t.Errorf("expected 1 signal type path for %s, got %d", isnSlug, len(perm.SignalTypePaths))
			continue
		}
		signalTypePath := perm.SignalTypePaths[0]
		if expectedPath, exists := validSignalTypePaths[isnSlug]; exists {
			if signalTypePath != expectedPath {
				t.Errorf("expected signal type path %s for %s, got %s", expectedPath, isnSlug, signalTypePath)
			}
		}
	}

	// validate JWT Token parses and validates the JWT token structure
	if response.AccessToken == "" {
		t.Fatalf("expected access token to be generated")
		return
	}

	// parse the claims (confirms signature and structure)
	claims := &auth.AccessTokenClaims{}
	_, err = jwt.ParseWithClaims(response.AccessToken, claims, func(token *jwt.Token) (any, error) {
		return []byte("test-secret-key"), nil
	})
	if err != nil {
		t.Fatalf("Failed to parse JWT token: %v", err)
	}

	// Check expiration - token should not be expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
		t.Error("JWT token is expired")
	}

	// Check issued at time - should be recent (within last minute)
	if claims.IssuedAt != nil {
		issuedAt := claims.IssuedAt.Time
		if time.Since(issuedAt) > time.Minute {
			t.Errorf("JWT issued at time %v is too old", issuedAt)
		}
	}

	// Verify claims match expected values
	if claims.AccountID != accountID {
		t.Errorf("expected account ID %s in JWT, got %s", accountID, claims.AccountID)
	}
	if claims.Role != expectedRole {
		t.Errorf("expected role %s in JWT, got %s", expectedRole, claims.Role)
	}

	// Verify standard JWT claims
	if claims.Subject != accountID.String() {
		t.Errorf("expected subject %s in JWT, got %s", accountID.String(), claims.Subject)
	}
	if claims.Issuer != "Signalsd" { // This should match signalsd.TokenIssuerName
		t.Errorf("expected issuer 'Signalsd' in JWT, got %s", claims.Issuer)
	}
}
